<!--测试审批模板API-->
<template>
  <div class="test-template-api">
    <a-card title="测试审批模板API">
      <a-space direction="vertical" style="width: 100%">
        <a-button type="primary" @click="testCheckActiveTemplate">测试 checkActiveTemplate API</a-button>
        <a-button type="primary" @click="testUserRoleList">测试 userRoleList API</a-button>
        <a-button type="primary" @click="testDynamicForm">测试动态表单生成</a-button>
        
        <a-divider />
        
        <div v-if="apiResult">
          <h4>API 返回结果:</h4>
          <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
        </div>
        
        <div v-if="formSchema">
          <h4>生成的表单结构:</h4>
          <pre>{{ JSON.stringify(formSchema, null, 2) }}</pre>
        </div>
        
        <div v-if="errorMessage">
          <a-alert :message="errorMessage" type="error" />
        </div>
      </a-space>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { checkActiveTemplate } from '/@/views/emsrepairorders/EmsRepairOrders.api';
  import { getDynamicFormSchema } from '/@/views/emsrepairorders/EmsRepairOrders.data';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();

  const apiResult = ref<any>(null);
  const formSchema = ref<any>(null);
  const errorMessage = ref<string>('');

  const testCheckActiveTemplate = async () => {
    try {
      errorMessage.value = '';
      console.log('开始测试 checkActiveTemplate API...');
      
      const result = await checkActiveTemplate();
      console.log('checkActiveTemplate 返回结果:', result);
      
      apiResult.value = result;
      createMessage.success('API 调用成功');
    } catch (error) {
      console.error('checkActiveTemplate 调用失败:', error);
      errorMessage.value = `API 调用失败: ${error.message || '未知错误'}`;
      createMessage.error('API 调用失败');
    }
  };

  const testUserRoleList = async () => {
    try {
      errorMessage.value = '';
      console.log('开始测试 userRoleList API...');
      
      // 使用一个测试角色ID
      const result = await defHttp.get({
        url: '/sys/user/userRoleList',
        params: {
          pageNo: 1,
          pageSize: 10,
          roleId: '1' // 测试角色ID
        }
      });
      
      console.log('userRoleList 返回结果:', result);
      apiResult.value = result;
      createMessage.success('userRoleList API 调用成功');
    } catch (error) {
      console.error('userRoleList 调用失败:', error);
      errorMessage.value = `userRoleList API 调用失败: ${error.message || '未知错误'}`;
      createMessage.error('userRoleList API 调用失败');
    }
  };

  const testDynamicForm = async () => {
    try {
      errorMessage.value = '';
      console.log('开始测试动态表单生成...');
      
      // 先获取模板数据
      const templateResult = await checkActiveTemplate();
      console.log('获取到模板数据:', templateResult);
      
      // 生成动态表单
      const schema = getDynamicFormSchema(templateResult);
      console.log('生成的表单结构:', schema);
      
      formSchema.value = schema;
      apiResult.value = templateResult;
      createMessage.success('动态表单生成成功');
    } catch (error) {
      console.error('动态表单生成失败:', error);
      errorMessage.value = `动态表单生成失败: ${error.message || '未知错误'}`;
      createMessage.error('动态表单生成失败');
    }
  };
</script>

<style lang="less" scoped>
  .test-template-api {
    padding: 20px;
    
    pre {
      background: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      max-height: 400px;
      overflow: auto;
      font-size: 12px;
    }
  }
</style>
