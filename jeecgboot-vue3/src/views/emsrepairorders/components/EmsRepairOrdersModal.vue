<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="800" @ok="handleSubmit">
      <BasicForm @register="registerForm" name="EmsRepairOrdersForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
    import {ref, computed, unref, reactive} from 'vue';
    import {BasicModal, useModalInner} from '/@/components/Modal';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import {formSchema, getDynamicFormSchema} from '../EmsRepairOrders.data';
    import {saveOrUpdate, checkActiveTemplate} from '../EmsRepairOrders.api';
    import { useMessage } from '/@/hooks/web/useMessage';
    import { getDateByPicker } from '/@/utils';
    const { createMessage } = useMessage();
    // Emits声明
    const emit = defineEmits(['register','success']);
    const isUpdate = ref(true);
    const isDetail = ref(false);
    const templateData = ref<any>(null);

    //表单配置
    const [registerForm, { setProps,resetFields, setFieldsValue, validate, scrollToField, updateSchema }] = useForm({
        labelWidth: 150,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: {span: 24}
    });
    //表单赋值
    const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
        //重置表单
        await resetFields();
        setModalProps({confirmLoading: false,showCancelBtn:!!data?.showFooter,showOkBtn:!!data?.showFooter});
        isUpdate.value = !!data?.isUpdate;
        isDetail.value = !!data?.showFooter;

        if (unref(isUpdate)) {
            //表单赋值
            await setFieldsValue({
                ...data.record,
            });
        } else {
            // 新增时获取审批模板信息并更新表单结构
            try {
                console.log('开始获取审批模板信息...');
                const templateResult = await checkActiveTemplate();
                console.log('获取到的审批模板信息:', templateResult);

                // 检查返回的数据结构
                if (!templateResult) {
                    console.warn('模板结果为空');
                    throw new Error('未获取到模板数据');
                }

                templateData.value = templateResult;

                // 使用动态表单结构
                const dynamicSchema = getDynamicFormSchema(templateResult);
                console.log('生成的动态表单结构:', dynamicSchema);
                console.log('动态表单字段数量:', dynamicSchema.length);

                await updateSchema(dynamicSchema);

                // 设置默认值
                await setFieldsValue({
                    currentStatus: '1', // 默认状态为审核中
                });

                if (templateResult.selectedRoles && templateResult.selectedRoles.length > 0) {
                    createMessage.success(`已加载审批模板信息，包含${templateResult.selectedRoles.length}个审批步骤`);
                } else {
                    createMessage.warning('审批模板没有配置审批步骤');
                }
            } catch (error) {
                console.error('获取审批模板失败:', error);
                createMessage.error('获取审批模板失败: ' + (error.message || '未知错误'));
                // 使用默认表单结构
                console.log('使用默认表单结构');
                await updateSchema(formSchema);

                // 设置默认值
                await setFieldsValue({
                    currentStatus: '1', // 默认状态为审核中
                });
            }
        }
        // 隐藏底部时禁用整个表单
       setProps({ disabled: !data?.showFooter })
    });
    //日期个性化选择
    const fieldPickers = reactive({
    });
    //设置标题
    const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(isDetail) ? '详情' : '编辑'));
    //表单提交事件
    async function handleSubmit(v) {
        try {
            let values = await validate();
            // 预处理日期数据
            changeDateValue(values);
            setModalProps({confirmLoading: true});
            //提交表单
            await saveOrUpdate(values, isUpdate.value);
            //关闭弹窗
            closeModal();
            //刷新列表
            emit('success');
        } catch ({ errorFields }) {
           if (errorFields) {
             const firstField = errorFields[0];
             if (firstField) {
               scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
             }
           }
           return Promise.reject(errorFields);
        } finally {
            setModalProps({confirmLoading: false});
        }
    }

    /**
     * 处理日期值
     * @param formData 表单数据
     */
    const changeDateValue = (formData) => {
        if (formData && fieldPickers) {
            for (let key in fieldPickers) {
                if (formData[key]) {
                    formData[key] = getDateByPicker(formData[key], fieldPickers[key]);
                }
            }
        }
    };

</script>

<style lang="less" scoped>
	/** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
