<!--根据角色ID选择用户的模态框-->
<template>
  <div>
    <BasicModal
      v-bind="$attrs"
      @register="register"
      :title="modalTitle"
      :width="1000"
      wrapClassName="j-user-select-modal"
      @ok="handleOk"
      @cancel="handleCancel"
      :maxHeight="maxHeight"
      :centered="true"
      destroyOnClose
      @visible-change="visibleChange"
    >
      <div class="jeecg-basic-table-form-container">
        <a-form ref="formRef" @keyup.enter.native="searchQuery">
          <a-row :gutter="24">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="用户账号">
                <a-input placeholder="请输入用户账号" v-model:value="queryParam.username"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="用户姓名">
                <a-input placeholder="请输入用户姓名" v-model:value="queryParam.realname"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
                <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 用户列表 -->
      <BasicTable
        ref="tableRef"
        size="small"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="rowSelection"
        :scroll="tableScroll"
        @change="handleTableChange"
        :rowKey="(record) => record.id"
      />

      <!-- 已选择用户展示 -->
      <div v-if="showSelected && selectRows.length > 0" class="selected-users">
        <div class="selected-header">
          <h4>已选择用户 ({{ selectRows.length }})</h4>
          <a-button size="small" @click="clearSelected">清空</a-button>
        </div>
        <div class="selected-list">
          <a-tag 
            v-for="user in selectRows" 
            :key="user.id"
            closable
            @close="removeSelected(user)"
          >
            {{ user.realname }}({{ user.username }})
          </a-tag>
        </div>
      </div>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, watch, onMounted } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicTable } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { defHttp } from '/@/utils/http/axios';
  import { selectProps } from '/@/components/Form/src/jeecg/props/props';

  const { createMessage } = useMessage();

  // Props
  const props = defineProps({
    ...selectProps,
    modalTitle: {
      type: String,
      default: '根据角色选择用户',
    },
    excludeUserIdList: {
      type: Array,
      default: () => [],
    },
    roleId: {
      type: String,
      default: '',
    },
  });

  // Emits
  const emit = defineEmits(['register', 'getSelectResult', 'close']);

  // 表格配置
  const tableScroll = ref<any>({ x: false });
  const tableRef = ref();
  const maxHeight = ref(600);

  // 查询参数
  const queryParam = reactive({
    username: '',
    realname: '',
  });

  // 表格数据
  const dataSource = ref<any[]>([]);
  const loading = ref(false);
  const ipagination = reactive({
    current: 1,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30'],
    showTotal: (total, range) => {
      return range[0] + '-' + range[1] + ' 共' + total + '条';
    },
    showQuickJumper: true,
    showSizeChanger: true,
    total: 0,
  });

  // 表格列配置
  const columns = [
    {
      title: '用户账号',
      align: 'center',
      dataIndex: 'username',
      width: 120,
    },
    {
      title: '用户姓名',
      align: 'center',
      dataIndex: 'realname',
      width: 120,
    },
    {
      title: '所属部门',
      align: 'center',
      dataIndex: 'orgCodeTxt',
      width: 150,
    },
  ];

  // 选择相关
  const selectRows = ref<any[]>([]);
  const selectedRowKeys = ref<string[]>([]);
  const showSelected = computed(() => props.multi === true);

  // 行选择配置
  const rowSelection = computed(() => {
    if (props.multi === true) {
      return {
        type: 'checkbox',
        selectedRowKeys: selectedRowKeys.value,
        onChange: onSelectChange,
        onSelect: onSelect,
        onSelectAll: onSelectAll,
      };
    } else {
      return {
        type: 'radio',
        selectedRowKeys: selectedRowKeys.value,
        onChange: onSelectChange,
      };
    }
  });

  //注册弹框
  const [register, { closeModal }] = useModalInner(() => {
    if (window.innerWidth < 900) {
      tableScroll.value = { x: 900 };
    } else {
      tableScroll.value = { x: false };
    }
    if (props.roleId) {
      loadUserList();
    }
  });

  // 加载用户列表
  const loadUserList = async () => {
    if (!props.roleId) {
      createMessage.warning('请先指定角色ID');
      return;
    }
    
    try {
      loading.value = true;
      const params = {
        pageNo: ipagination.current,
        pageSize: ipagination.pageSize,
        roleId: props.roleId,
        ...queryParam,
      };

      if (props.excludeUserIdList && props.excludeUserIdList.length > 0) {
        params['excludeUserIdList'] = props.excludeUserIdList.join(',');
      }

      const result = await defHttp.get({
        url: '/sys/user/userRoleList',
        params,
      });

      if (result) {
        dataSource.value = result.records || [];
        ipagination.total = result.total || 0;
      }
    } catch (error) {
      console.error('加载用户列表失败:', error);
      createMessage.error('加载用户列表失败');
    } finally {
      loading.value = false;
    }
  };

  // 查询
  const searchQuery = () => {
    ipagination.current = 1;
    loadUserList();
  };

  // 重置
  const searchReset = () => {
    queryParam.username = '';
    queryParam.realname = '';
    searchQuery();
  };

  // 表格变化
  const handleTableChange = (pagination) => {
    ipagination.current = pagination.current;
    ipagination.pageSize = pagination.pageSize;
    loadUserList();
  };

  // 选择变化
  const onSelectChange = (keys: string[], rows: any[]) => {
    selectedRowKeys.value = keys;
    selectRows.value = rows;
  };

  const onSelect = (record: any, selected: boolean) => {
    if (selected) {
      if (!selectRows.value.find(item => item.id === record.id)) {
        selectRows.value.push(record);
      }
    } else {
      const index = selectRows.value.findIndex(item => item.id === record.id);
      if (index > -1) {
        selectRows.value.splice(index, 1);
      }
    }
  };

  const onSelectAll = (selected: boolean, selectedRows: any[], changeRows: any[]) => {
    if (selected) {
      changeRows.forEach(row => {
        if (!selectRows.value.find(item => item.id === row.id)) {
          selectRows.value.push(row);
        }
      });
    } else {
      changeRows.forEach(row => {
        const index = selectRows.value.findIndex(item => item.id === row.id);
        if (index > -1) {
          selectRows.value.splice(index, 1);
        }
      });
    }
  };

  // 移除选中
  const removeSelected = (user: any) => {
    const index = selectRows.value.findIndex(item => item.id === user.id);
    if (index > -1) {
      selectRows.value.splice(index, 1);
      selectedRowKeys.value = selectRows.value.map(item => item.id);
    }
  };

  // 清空选中
  const clearSelected = () => {
    selectRows.value = [];
    selectedRowKeys.value = [];
  };

  // 确定选择
  const handleOk = () => {
    const selectedUsers = selectRows.value;
    const values = selectedUsers.map(user => user[props.rowKey || 'id']);
    emit('getSelectResult', selectedUsers, values);
    closeModal();
  };

  // 取消
  const handleCancel = () => {
    emit('close');
    closeModal();
  };

  // 可见性变化
  const visibleChange = (visible: boolean) => {
    if (!visible) {
      // 重置状态
      dataSource.value = [];
      selectRows.value = [];
      selectedRowKeys.value = [];
      queryParam.username = '';
      queryParam.realname = '';
    }
  };
</script>

<style lang="less" scoped>
  .selected-users {
    margin-top: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    
    .selected-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
      
      h4 {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
      }
    }
    
    .selected-list {
      .ant-tag {
        margin-bottom: 8px;
      }
    }
  }
</style>
