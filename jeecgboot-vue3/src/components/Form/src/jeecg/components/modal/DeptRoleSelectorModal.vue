<!--部门角色选择弹窗-->
<template>
  <BasicModal
    v-bind="$attrs"
    @register="register"
    title="部门角色选择"
    width="800px"
    :maxHeight="600"
    @ok="handleOk"
    @cancel="handleCancel"
    destroyOnClose
  >
    <div class="dept-role-selector-modal">
      <a-row :gutter="16">
        <!-- 左侧部门选择 -->
        <a-col :span="10">
          <div class="section">
            <div class="section-title">
              <Icon icon="ant-design:apartment-outlined" />
              选择部门
            </div>
            <div class="dept-tree-container">
              <BasicTree
                ref="deptTreeRef"
                :treeData="deptTreeData"
                :fieldNames="{ key: 'key', title: 'title', children: 'children' }"
                @select="onDeptSelect"
                :selectedKeys="selectedDeptKeys"
                :checkable="false"
                :showLine="true"
                :showIcon="false"
              />
            </div>
          </div>
        </a-col>

        <!-- 右侧角色选择和排序 -->
        <a-col :span="14">
          <div class="section">
            <div class="section-title">
              <Icon icon="ant-design:team-outlined" />
              选择角色
              <span v-if="selectedDept.departName" class="dept-info">
                ({{ selectedDept.departName }})
              </span>
            </div>

            <!-- 可选角色列表 -->
            <div v-if="selectedDept.id" class="role-selection">
              <div class="available-roles">
                <div class="sub-title">可选角色</div>
                <div class="role-list">
                  <div
                    v-for="role in availableRoles"
                    :key="role.id"
                    class="role-item"
                    :class="{ selected: isRoleSelected(role.id) }"
                    @click="toggleRole(role)"
                  >
                    <a-checkbox :checked="isRoleSelected(role.id)" />
                    <div class="role-info">
                      <div class="role-name">{{ role.roleName }}</div>
                      <div class="role-code">{{ role.roleCode }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 已选角色排序 -->
              <div v-if="selectedRoles.length > 0" class="selected-roles">
                <div class="sub-title">
                  已选角色 ({{ selectedRoles.length }})
                  <span class="tip">可拖拽调整顺序</span>
                </div>
                <draggable
                  v-model="selectedRoles"
                  item-key="id"
                  class="role-sort-list"
                  :animation="200"
                  ghost-class="ghost"
                >
                  <template #item="{ element, index }">
                    <div class="selected-role-item">
                      <Icon icon="ant-design:drag-outlined" class="drag-handle" />
                      <div class="role-info">
                        <div class="role-name">{{ element.roleName }}</div>
                        <div class="role-code">{{ element.roleCode }}</div>
                      </div>
                      <div class="sort-order">{{ index + 1 }}</div>
                      <Icon
                        icon="ant-design:close-outlined"
                        class="remove-btn"
                        @click="removeRole(element.id)"
                      />
                    </div>
                  </template>
                </draggable>
              </div>
            </div>

            <!-- 未选择部门提示 -->
            <div v-else class="no-dept-selected">
              <a-empty description="请先选择部门" />
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicTree } from '/@/components/Tree';
import { Icon } from '/@/components/Icon';
import draggable from 'vuedraggable';
import { queryDepartTreeSync } from '/@/api/common/api';
import { getDeptRoleListBySysOrgCode, getDeptRoleListByDepartId } from './api/deptRoleApi';

interface RoleItem {
  id: string;
  roleName: string;
  roleCode: string;
  description?: string;
  sortOrder?: number;
}

interface DeptItem {
  id: string;
  departName: string;
  orgCode: string;
  children?: DeptItem[];
}

interface SelectedData {
  departId?: string;
  departName?: string;
  sysOrgCode?: string;
  roles?: RoleItem[];
}

const emit = defineEmits(['register', 'confirm']);

// 注册弹窗
const [register, { closeModal }] = useModalInner((data: SelectedData) => {
  initModalData(data);
});

// 部门树数据
const deptTreeData = ref<DeptItem[]>([]);
const deptTreeRef = ref();

// 选中的部门
const selectedDept = reactive<{
  id: string;
  departName: string;
  orgCode: string;
}>({
  id: '',
  departName: '',
  orgCode: ''
});

const selectedDeptKeys = ref<string[]>([]);

// 角色相关数据
const availableRoles = ref<RoleItem[]>([]);
const selectedRoles = ref<RoleItem[]>([]);

// 初始化弹窗数据
const initModalData = async (data: SelectedData) => {
  // 加载部门树
  await loadDeptTree();

  // 如果有传入数据，恢复选择状态
  if (data?.departId) {
    selectedDept.id = data.departId;
    selectedDept.departName = data.departName || '';
    selectedDept.orgCode = data.sysOrgCode || '';
    selectedDeptKeys.value = [data.departId];

    // 加载部门角色
    await loadDeptRoles(data.departId);

    // 恢复已选角色
    if (data.roles && data.roles.length > 0) {
      selectedRoles.value = [...data.roles];
    }
  }
};

// 加载部门树
const loadDeptTree = async () => {
  try {
    const result = await queryDepartTreeSync();
    console.log('部门树数据:', result);
    deptTreeData.value = result || [];
  } catch (error) {
    console.error('加载部门树失败:', error);
    deptTreeData.value = [];
  }
};

// 部门选择事件
const onDeptSelect = async (selectedKeys: string[], info: any) => {
  if (selectedKeys.length > 0) {
    const selectedNode = info.node;
    selectedDept.id = selectedNode.id || selectedKeys[0];
    selectedDept.departName = selectedNode.departName || selectedNode.title;
    selectedDept.orgCode = selectedNode.orgCode || selectedNode.key;
    selectedDeptKeys.value = selectedKeys;

    console.log('部门选择事件，选中的部门信息:', {
      selectedKeys,
      nodeId: selectedNode.id,
      departName: selectedNode.departName || selectedNode.title,
      orgCode: selectedNode.orgCode,
      key: selectedNode.key,
      fullNode: selectedNode
    });

    // 清空之前选择的角色
    selectedRoles.value = [];

    // 加载部门角色
    await loadDeptRoles(selectedKeys[0]);
  }
};

// 加载部门角色列表
const loadDeptRoles = async (departId: string) => {
  try {
    console.log('开始加载部门角色，部门ID:', departId);

    // 根据部门ID查找对应的部门节点，获取orgCode
    const deptNode = findDeptNode(deptTreeData.value, departId);
    if (!deptNode) {
      console.warn('未找到部门节点，部门ID:', departId);
      availableRoles.value = [];
      return;
    }

    // 获取sysOrgCode，优先使用orgCode，其次使用key
    const sysOrgCode = deptNode.orgCode || deptNode.key;
    if (!sysOrgCode) {
      console.warn('未找到部门的orgCode，部门节点:', deptNode);
      availableRoles.value = [];
      return;
    }

    console.log('部门节点信息:', {
      departId,
      departName: deptNode.departName || deptNode.title,
      orgCode: deptNode.orgCode,
      key: deptNode.key,
      使用的sysOrgCode: sysOrgCode
    });

    // 使用sysOrgCode查询角色
    const result = await getDeptRoleListBySysOrgCode({ sysOrgCode });
    availableRoles.value = result || [];

    console.log('通过sysOrgCode查询到的部门角色:', result);

  } catch (error) {
    console.error('加载部门角色失败:', error);
    availableRoles.value = [];
  }
};

// 递归查找部门节点
const findDeptNode = (nodes: any[], targetId: string): any | null => {
  for (const node of nodes) {
    if (node.id === targetId || node.key === targetId) {
      return node;
    }
    if (node.children) {
      const found = findDeptNode(node.children, targetId);
      if (found) return found;
    }
  }
  return null;
};

// 判断角色是否已选中
const isRoleSelected = (roleId: string) => {
  return selectedRoles.value.some(role => role.id === roleId);
};

// 切换角色选择状态
const toggleRole = (role: RoleItem) => {
  const index = selectedRoles.value.findIndex(r => r.id === role.id);
  if (index > -1) {
    selectedRoles.value.splice(index, 1);
  } else {
    selectedRoles.value.push({ ...role });
  }
};

// 移除角色
const removeRole = (roleId: string) => {
  const index = selectedRoles.value.findIndex(r => r.id === roleId);
  if (index > -1) {
    selectedRoles.value.splice(index, 1);
  }
};

// 确认选择
const handleOk = () => {
  if (!selectedDept.id) {
    return Promise.reject('请选择部门');
  }

  const result: SelectedData = {
    departId: selectedDept.id,
    departName: selectedDept.departName,
    sysOrgCode: selectedDept.orgCode,
    roles: selectedRoles.value.map((role, index) => ({
      ...role,
      sortOrder: index + 1
    }))
  };

  emit('confirm', result);
  closeModal();
};

// 取消
const handleCancel = () => {
  closeModal();
};
</script>

<style lang="less" scoped>
.dept-role-selector-modal {
  .section {
    height: 500px;

    .section-title {
      display: flex;
      align-items: center;
      font-weight: 500;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      .anticon {
        margin-right: 6px;
        color: #1890ff;
      }

      .dept-info {
        color: #8c8c8c;
        font-weight: normal;
        margin-left: 4px;
      }
    }
  }

  .dept-tree-container {
    height: 460px;
    overflow-y: auto;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    padding: 8px;
  }

  .role-selection {
    height: 460px;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .sub-title {
      font-weight: 500;
      color: #262626;

      .tip {
        font-size: 12px;
        color: #8c8c8c;
        font-weight: normal;
        margin-left: 8px;
      }
    }

    .available-roles {
      flex: 1;

      .role-list {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid #f0f0f0;
        border-radius: 4px;

        .role-item {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          cursor: pointer;
          transition: background-color 0.3s;

          &:hover {
            background-color: #f5f5f5;
          }

          &.selected {
            background-color: #e6f7ff;
          }

          .ant-checkbox {
            margin-right: 8px;
          }

          .role-info {
            .role-name {
              font-weight: 500;
              color: #262626;
            }

            .role-code {
              font-size: 12px;
              color: #8c8c8c;
            }
          }
        }
      }
    }

    .selected-roles {
      flex: 1;

      .role-sort-list {
        max-height: 240px;
        overflow-y: auto;
        border: 1px solid #f0f0f0;
        border-radius: 4px;

        .selected-role-item {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          background: #fafafa;
          border-bottom: 1px solid #f0f0f0;
          cursor: move;

          &:last-child {
            border-bottom: none;
          }

          .drag-handle {
            margin-right: 8px;
            color: #8c8c8c;
            cursor: grab;

            &:active {
              cursor: grabbing;
            }
          }

          .role-info {
            flex: 1;

            .role-name {
              font-weight: 500;
              color: #262626;
            }

            .role-code {
              font-size: 12px;
              color: #8c8c8c;
            }
          }

          .sort-order {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #1890ff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-right: 8px;
          }

          .remove-btn {
            color: #ff4d4f;
            cursor: pointer;

            &:hover {
              color: #ff7875;
            }
          }
        }

        .ghost {
          opacity: 0.5;
          background: #c8ebfb;
        }
      }
    }
  }

  .no-dept-selected {
    height: 460px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
